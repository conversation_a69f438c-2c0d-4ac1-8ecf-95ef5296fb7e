import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Input } from '@/components/ui/Input';
import { useTheme } from '@/contexts/ThemeContext';
import { supabase } from '@/lib/supabase';

export default function ForgotPasswordScreen() {
  const { colors } = useTheme();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);

  const handleResetPassword = async () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    try {
      setLoading(true);
      const { error } = await supabase.auth.resetPasswordForEmail(email.trim(), {
        redirectTo: 'footfit://reset-password',
      });

      if (error) {
        Alert.alert('Error', error.message);
      } else {
        setSent(true);
      }
    } catch {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToLogin = () => {
    router.back();
  };

  if (sent) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedView style={styles.content}>
          <View style={styles.successContainer}>
            <View style={[styles.successIcon, { backgroundColor: colors.success + '20' }]}>
              <IconSymbol
                size={48}
                name="checkmark.circle.fill"
                color={colors.success}
              />
            </View>
            
            <ThemedText variant="h3" style={styles.successTitle}>
              Check Your Email
            </ThemedText>
            
            <ThemedText variant="body" color="secondary" style={styles.successMessage}>
              We&apos;ve sent a password reset link to {email}. Please check your email and follow the instructions to reset your password.
            </ThemedText>

            <View style={styles.successActions}>
              <Button
                title="Back to Login"
                onPress={handleBackToLogin}
                variant="primary"
                size="large"
                fullWidth
              />
              
              <Button
                title="Resend Email"
                onPress={() => {
                  setSent(false);
                  handleResetPassword();
                }}
                variant="ghost"
                size="medium"
              />
            </View>
          </View>
        </ThemedView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedView style={styles.content}>
        {/* Header */}
        <ThemedView style={styles.header}>
          <Button
            title=""
            onPress={handleBackToLogin}
            variant="ghost"
            size="small"
            icon="chevron.left"
            style={styles.backButton}
          />
          
          <View style={[styles.icon, { backgroundColor: colors.primary + '20' }]}>
            <IconSymbol
              size={32}
              name="key.fill"
              color={colors.primary}
            />
          </View>
          
          <ThemedText variant="h2" style={styles.title}>
            Reset Password
          </ThemedText>
          
          <ThemedText variant="body" color="secondary" style={styles.subtitle}>
            Enter your email address and we'll send you a link to reset your password.
          </ThemedText>
        </ThemedView>

        {/* Form */}
        <ThemedView style={styles.form}>
          <Input
            label="Email"
            placeholder="Enter your email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            leftIcon="envelope.fill"
            required
          />

          <Button
            title="Send Reset Link"
            onPress={handleResetPassword}
            variant="primary"
            size="large"
            loading={loading}
            disabled={loading}
            fullWidth
            icon="paperplane.fill"
            style={styles.resetButton}
          />
        </ThemedView>

        {/* Footer */}
        <ThemedView style={styles.footer}>
          <ThemedText variant="body" color="secondary" style={styles.footerText}>
            Remember your password?
          </ThemedText>
          <Button
            title="Back to Login"
            onPress={handleBackToLogin}
            variant="outline"
            size="medium"
          />
        </ThemedView>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: 40,
  },
  icon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    maxWidth: 280,
  },
  form: {
    gap: 16,
    marginBottom: 32,
  },
  resetButton: {
    marginTop: 8,
  },
  footer: {
    alignItems: 'center',
    gap: 12,
    marginTop: 'auto',
  },
  footerText: {
    textAlign: 'center',
  },
  successContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 24,
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successTitle: {
    textAlign: 'center',
  },
  successMessage: {
    textAlign: 'center',
    maxWidth: 320,
    lineHeight: 24,
  },
  successActions: {
    width: '100%',
    gap: 12,
    marginTop: 16,
  },
});
